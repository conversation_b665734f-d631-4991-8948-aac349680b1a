<?php
include 'db.php'; // connect to database

// When form is submitted (Add task)
if (isset($_POST['task'])) {
    $task = $_POST['task']; // get the task text

    // Show what user typed using a popup
    echo "<script>alert('You typed: " . addslashes($task) . "');</script>";

    if (!empty($task)) {
        $conn->query("INSERT INTO tasks (description) VALUES ('$task')");
    }
}

// If delete link is clicked
if (isset($_GET['delete'])) {
    $id = $_GET['delete'];
    $conn->query("DELETE FROM tasks WHERE id = $id");
}

// Get all tasks to display
$tasks = $conn->query("SELECT * FROM tasks");
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple To-Do List</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="todo-container">
        <h2>To-Do List</h2>

        <form method="post">
            <input type="text" name="task" placeholder="Enter a task..." required>
            <input type="submit" value="Add">
        </form>

        <ul>
            <?php while ($row = $tasks->fetch_assoc()): ?>
                <li>
                    <?php echo htmlspecialchars($row['description']); ?>
                    <a href="?delete=<?php echo $row['id']; ?>">Delete</a>
                </li>
            <?php endwhile; ?>
        </ul>
    </div>
</body>
</html>
