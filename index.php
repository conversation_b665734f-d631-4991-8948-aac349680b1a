<?php
include 'db.php';

// Add task
if (isset($_POST['task']) && !empty($_POST['task'])) {
    $task = $_POST['task'];
    $conn->query("INSERT INTO tasks (description) VALUES ('$task')");
}

// Delete task
if (isset($_GET['delete'])) {
    $id = $_GET['delete'];
    $conn->query("DELETE FROM tasks WHERE id = $id");
}

// Get all tasks
$tasks = $conn->query("SELECT * FROM tasks");
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple To-Do List</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="todo-container">
        <h2>To-Do List</h2>

        <form method="post">
            <input type="text" name="task" placeholder="Enter a task..." required>
            <input type="submit" value="Add">
        </form>

        <ul>
            <?php while ($row = $tasks->fetch_assoc()): ?>
                <li>
                    <?php echo htmlspecialchars($row['description']); ?>
                    <a href="?delete=<?php echo $row['id']; ?>">Delete</a>
                </li>
            <?php endwhile; ?>
        </ul>
    </div>
</body>
</html>
